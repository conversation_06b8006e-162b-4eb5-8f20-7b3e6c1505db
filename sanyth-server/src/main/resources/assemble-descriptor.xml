<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>bin</id>
	<formats>
<!--		<format>tar.gz</format>-->
		<format>zip</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>src/main/resources/bin</directory>
			<outputDirectory>sanyth-dev-platform/bin</outputDirectory>
			<includes>
				<include>*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/</directory>
			<outputDirectory>sanyth-dev-platform/conf</outputDirectory>
			<includes>
				<include>*.yml</include>
				<include>logback-spring.xml</include>
				<include>conf.properties</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/static</directory>
			<outputDirectory>sanyth-dev-platform/static</outputDirectory>
			<includes>
				<include>*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>true</filtered>
		</fileSet>
		<fileSet>
			<directory>src/main/resources/templates</directory>
			<outputDirectory>sanyth-dev-platform/web/templates</outputDirectory>
			<includes>
				<include>**/*.*</include>
			</includes>
			<fileMode>0755</fileMode>
			<filtered>false</filtered>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<outputDirectory>sanyth-dev-platform/lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
		<dependencySet>
			<outputDirectory>sanyth-dev-platform/lib</outputDirectory>
			<scope>system</scope>
		</dependencySet>
	</dependencySets>

</assembly>