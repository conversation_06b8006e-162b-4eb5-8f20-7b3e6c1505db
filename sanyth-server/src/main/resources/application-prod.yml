# 开发环境配置

# 数据源配置
spring:
  datasource:
    url: *****************************************
    username: syt_aigc
    password: syt2025syt_aigc
    driver-class-name: oracle.jdbc.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-properties: oracle.jdbc.timezoneAsRegion=true
      filters: stat,log4j
      validation-query: SELECT 1 FROM DUAL
  data:
    mongodb:
      host: **************
      port: 29019
      authentication-database: admin
      database: server
      username: admin
      password: "sanyth123456"
      auto-index-creation: true  # 启用自动创建索引
    redis:
      host: **************
      port: 6379
      password: sanyth123456
      timeout: 10000
      database: 11

# 日志配置
logging:
  level:
    com.sanyth: info
