# 开发环境配置

# 数据源配置
spring:
  datasource:
#    url: ********************************************
#    username: syt_dev_platform
#    password: syt_dev_platform_2025
    url: ************************************
    username: syt_aiass
    password: Sanyth_2507#Aiass
    driver-class-name: oracle.jdbc.OracleDriver
#    url: jdbc:dm://************:30236/syt_dev_platform?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true
#    username: syt_dev_platform
#    password: syt_dev_platform
#    driver-class-name: dm.jdbc.driver.DmDriver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      connection-properties: oracle.jdbc.timezoneAsRegion=true
      filters: stat,log4j
      validation-query: SELECT 1 FROM DUAL
  data:
    mongodb:
#      host: *************
#      port: 37017
#      authentication-database: admin
#      database: server
#      username: admin
#      password: "123456"
      host: 127.0.0.1
      port: 28018
      authentication-database: admin
      database: server
      username: admin
      password: "sanyth123"
      # 索引配置
      auto-index-creation: true         # 是否自动创建索引
      # UUID 表示方式配置
      uuid-representation: JAVA_LEGACY  # UUID表示方式：JAVA_LEGACY, C_SHARP_LEGACY, PYTHON_LEGACY, STANDARD
      # GridFS 配置
      gridfs:
        bucket: fs                  # GridFS bucket 名称，默认为 fs
        chunk-size-bytes: 524288    # 块大小，默认 255KB
      # SSL 配置
      ssl:
        enabled: false                  # 是否启用 SSL
#        bundle: your_bundle             # SSL bundle 名称
#        invalid-hostname-allowed: false # 是否允许无效的主机名
    redis:
      host: *************
      port: 6380
      password: 123456
      timeout: 10000
      database: 11

# 日志配置
logging:
  level:
    com.sanyth: debug
    com.baomidou.mybatisplus: debug
    dev:
      langchain4j: DEBUG
      ai4j:
        openai4j: DEBUG

# 语音识别配置
speech:
  # 默认语音识别服务提供商 (baidu, xfyun, aliyun)
  default-provider: ${SPEECH_DEFAULT_PROVIDER:xfyun}
  # 最小有效音频大小(字节)，小于此值视为无效音频
  min-audio-size: ${SPEECH_MIN_AUDIO_SIZE:1024}
  # 是否启用服务商故障转移
  fallback-enabled: ${SPEECH_FALLBACK_ENABLED:false}
  # 最大重试次数
  max-retries: ${SPEECH_MAX_RETRIES:2}
  # 是否优先使用浏览器原生语音识别
  use-browser-recognition: ${SPEECH_USE_BROWSER:true}
  # 最大录音时长(秒)
  max-recording-time: ${SPEECH_MAX_RECORDING_TIME:60}

  # 百度语音识别配置
  baidu:
    app-id: ${SPEECH_BAIDU_APP_ID:}
    api-key: ${SPEECH_BAIDU_API_KEY:}
    secret-key: ${SPEECH_BAIDU_SECRET_KEY:}

  # 讯飞语音识别配置
  xfyun:
    app-id: ${SPEECH_XFYUN_APP_ID:0e623c28}
    api-key: ${SPEECH_XFYUN_API_KEY:cff52869952df2be4751e1724e8e4ea7}
    api-secret: ${SPEECH_XFYUN_API_SECRET:MWMwMzUxZTk0Y2JmYWMwNDYwYjBlZDQx}

  # 阿里云语音识别配置
  aliyun:
    access-key-id: ${SPEECH_ALIYUN_ACCESS_KEY_ID:}
    access-key-secret: ${SPEECH_ALIYUN_ACCESS_KEY_SECRET:}
    app-key: ${SPEECH_ALIYUN_APP_KEY:}
