/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 */
body {
  background-color: #2f3742;
  color: #dfe0e4;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.4;
  margin: 1rem;
}
a {
  color: #4099ff;
}
table {
  border-collapse: collapse;
}
table:not([cellpadding]) td,
table:not([cellpadding]) th {
  padding: 0.4rem;
}
table[border]:not([border='0']):not([style*='border-width']) td,
table[border]:not([border='0']):not([style*='border-width']) th {
  border-width: 1px;
}
table[border]:not([border='0']):not([style*='border-style']) td,
table[border]:not([border='0']):not([style*='border-style']) th {
  border-style: solid;
}
table[border]:not([border='0']):not([style*='border-color']) td,
table[border]:not([border='0']):not([style*='border-color']) th {
  border-color: #6d737b;
}
figure {
  display: table;
  margin: 1rem auto;
}
figure figcaption {
  color: #8a8f97;
  display: block;
  margin-top: 0.25rem;
  text-align: center;
}
hr {
  border-color: #6d737b;
  border-style: solid;
  border-width: 1px 0 0 0;
}
code {
  background-color: #6d737b;
  border-radius: 3px;
  padding: 0.1rem 0.2rem;
}
.mce-content-body:not([dir='rtl']) blockquote {
  border-left: 2px solid #6d737b;
  margin-left: 1.5rem;
  padding-left: 1rem;
}
.mce-content-body[dir='rtl'] blockquote {
  border-right: 2px solid #6d737b;
  margin-right: 1.5rem;
  padding-right: 1rem;
}
