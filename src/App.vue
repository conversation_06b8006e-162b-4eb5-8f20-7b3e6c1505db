<template>
  <el-config-provider :locale="elLocale">
    <ele-config-provider
      :locale="eleLocale"
      :map-key="MAP_KEY"
      :table="tableConfig"
      :license="LICENSE_CODE"
    >
      <ele-app>
        <router-view />
      </ele-app>
    </ele-config-provider>
  </el-config-provider>
</template>

<script setup>
  import { useThemeStore } from '@/store/modules/theme';
  import { LICENSE_CODE, MAP_KEY } from '@/config/setting';
  import { useLocale } from '@/i18n/use-locale';
  import {EleApp, EleConfigProvider} from 'ele-admin-plus';
  import {onMounted, provide, ref} from 'vue';
  import {getSystemInfo} from '@/api/login/index.js';

  // App.vue
  const refreshFlag = ref(false);
  provide('refreshEvent', {
    refreshFlag, // 提供响应式 ref
    triggerRefresh: () => {
      refreshFlag.value = !refreshFlag.value;
    },
  });

  window._AMapSecurityConfig = {
    securityJsCode: '329977d48d064c5483498a44d3618d98'
  };

  // 恢复主题
  const themeStore = useThemeStore();
  themeStore.recoverTheme();

  // 国际化配置
  const { elLocale, eleLocale } = useLocale();

  // 高级表格全局配置
  const tableConfig = ref({
    response: {
      dataName: 'list',
      countName: 'count'
    }
  });

  const systemInfo = ref([]);
  const initSystemInfo = async () => {
    systemInfo.value = await getSystemInfo({});
    // 调用设置 favicon 函数
    setDynamicFavicon();
  };

  // // 在创建应用后立即设置动态 favicon
  function setDynamicFavicon() {
    // 从 store 中获取 systemInfo
    let faviconUrl = '/favicon.ico'; // 默认 favicon
    // 检查是否有自定义 favicon 配置
    if (systemInfo.value.favicon?.attachment) {
      try {
        const attachments = JSON.parse(systemInfo.value.favicon.attachment);
        if (attachments && attachments.length > 0) {
          faviconUrl = `/api/file/inline/${attachments[0].id}`;
        }
      } catch (e) {
        console.error('解析 favicon 附件失败:', e);
      }
    }
    // 创建或更新 favicon link 元素
    let link = document.querySelector('link[rel*=\'icon\']') ||
      document.createElement('link');
    link.type = 'image/x-icon';
    link.rel = 'shortcut icon';
    link.href = faviconUrl;

    // 添加到 head
    document.getElementsByTagName('head')[0].appendChild(link);
    // 添加时间戳避免缓存
    const timestamp = new Date().getTime();
    link.href = `${faviconUrl}?t=${timestamp}`;
  }

  // 初始化
  onMounted(() => {
    initSystemInfo();
  });
</script>

<style lang="scss">
  /**隐藏高德自带的logo*/
  .amap-logo {
    display: none !important;
  }

  .amap-copyright {
    bottom: -100px;
    display: none !important;
  }

  .ele-drawer-header {
    padding: 10px 16px !important;
  }

  .ele-drawer-body {
    padding: 16px !important;
  }

  .ele-card-header {
    padding: 5px 10px !important;
  }

  .ele-card-body {
    padding: 16px 0 16px 0 !important;
  }

  .el-form-item {
    margin-bottom: 15px !important;
  }
</style>
