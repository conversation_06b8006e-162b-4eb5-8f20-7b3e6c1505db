package com.sanyth.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanyth.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum Required implements ValueTextBaseEnum {
    yes(1, "必填", true), no(0, "非必填", false);
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private Boolean mark;

    Required(Integer value, String text, Boolean mark) {
        this.value = value;
        this.text = text;
        this.mark = mark;
    }

    public Boolean getMark() {
        return mark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
