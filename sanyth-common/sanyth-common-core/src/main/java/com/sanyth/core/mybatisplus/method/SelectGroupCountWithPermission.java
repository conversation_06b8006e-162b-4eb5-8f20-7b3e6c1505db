package com.sanyth.core.mybatisplus.method;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.sanyth.core.mybatisplus.MySqlMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.Map;

/**
 * Created by JIANGPING on 2024/12/5.
 */
public class SelectGroupCountWithPermission extends AbstractMethod {
    protected static final MySqlMethod sqlMethod = MySqlMethod.SELECT_GROUP_COUNT_WITH_PERMISSION;

    public SelectGroupCountWithPermission() {
        super(sqlMethod.getMethod());
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(sqlMethod.getSql(), this.sqlFirst(), this.sqlSelectColumns(tableInfo, true), tableInfo.getTableName(), this.sqlWhereEntityWrapper(true, tableInfo), this.sqlComment());
        SqlSource sqlSource = super.createSqlSource(this.configuration, sql, modelClass);
        return this.addSelectMappedStatementForOther(mapperClass, this.methodName, sqlSource, Map.class);
    }
}
