package com.sanyth.core.config.enumConverterFactory;

import com.sanyth.core.exception.BusinessException;
import org.springframework.core.convert.converter.Converter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class ValueTextToEnumConverter<T extends ValueTextBaseEnum> implements Converter<String, T> {
    private final Map<String, T> idEnumMap = new HashMap<>();
    private final Map<String, T> codeEnumMap = new HashMap<>();

    public ValueTextToEnumConverter(Class<T> enumType) {
        Arrays.stream(enumType.getEnumConstants())
                .forEach(x -> {
                    idEnumMap.put(x.getValue().toString(), x);
                    codeEnumMap.put(x.getText(), x);
                });
    }

    @Override
    public T convert(String source) {
        return Optional.of(source)
                .map(codeEnumMap::get)
                .orElseGet(() -> Optional.of(source)
                        .map(idEnumMap::get)
                        .orElseThrow(() -> new BusinessException("参数类型错误")));
    }
}
