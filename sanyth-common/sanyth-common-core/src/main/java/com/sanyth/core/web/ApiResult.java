package com.sanyth.core.web;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * 返回结果
 *
 * <AUTHOR>
 * @since 2017-06-10 10:10:50
 */
@JsonIgnoreProperties({"mask"})
public class ApiResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 状态信息
     */
    private String message;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 错误信息
     */
    private String error;
    /**
     * 脱敏
     */
    private Boolean mask;

    public ApiResult() {
    }

    public ApiResult(Integer code) {
        this(code, null);
    }

    public ApiResult(Integer code, String message) {
        this(code, message, null);
    }

    public ApiResult(Integer code, String message, T data) {
        this(code, message, data, null);
    }

    public ApiResult(Integer code, String message, T data, String error) {
        setCode(code);
        setMessage(message);
        setData(data);
        setError(error);
    }

    public Integer getCode() {
        return this.code;
    }

    public ApiResult<T> setCode(Integer code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return this.message;
    }

    public ApiResult<T> setMessage(String message) {
        this.message = message;
        return this;
    }

    public T getData() {
        return this.data;
    }

    public ApiResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public String getError() {
        return this.error;
    }

    public ApiResult<T> setError(String error) {
        this.error = error;
        return this;
    }

    public Boolean getMask() {
        return mask;
    }

    public ApiResult<T> setMask(Boolean mask) {
        this.mask = mask;
        return this;
    }
}
