package com.sanyth.core.config;

import com.mongodb.MongoException;
import com.mongodb.MongoGridFSException;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.validation.annotation.Validated;

@Configuration
@Slf4j
@ConfigurationProperties(prefix = "spring.data.mongodb")
@Validated  // 启用配置验证
public class MongoConfig {

    /**
     * 数据库名称
     */
    @NotBlank(message = "数据库名称不能为空")
    private String database;

    /**
     * GridFS bucket 名称，默认为 fs
     */
    @Value("${spring.data.mongodb.gridfs.bucket:fs}")
    private String bucketName;

    /**
     * 块大小（bytes），默认为 255KB
     */
    @Value("${spring.data.mongodb.gridfs.chunk-size-bytes:261120}")
    private int chunkSizeBytes;

    /**
     * 配置 GridFSBucket
     */
    @Bean
    public GridFSBucket gridFSBucket(MongoClient mongoClient) {
        try {
            MongoDatabase mongoDatabase = mongoClient.getDatabase(database);

            // 创建 GridFSBucket 并配置选项
            GridFSBucket bucket = GridFSBuckets.create(mongoDatabase, bucketName)
                    .withChunkSizeBytes(chunkSizeBytes)
                    .withReadPreference(ReadPreference.primaryPreferred())
                    .withWriteConcern(WriteConcern.ACKNOWLEDGED);

            log.info("GridFSBucket 初始化成功 - 数据库: {}, Bucket: {}, 块大小: {}KB",
                    database, bucketName, chunkSizeBytes / 1024);

            return bucket;

        } catch (Exception e) {
            log.error("GridFSBucket 初始化失败: {}", e.getMessage());
            throw new MongoGridFSException("GridFSBucket 初始化失败: " + e.getMessage());
        }
    }

    /**
     * 配置自定义的 MongoTemplate
     */
    @Bean
    public MongoTemplate mongoTemplate(MongoClient mongoClient) {
        try {
            return new MongoTemplate(mongoClient, database);
        } catch (Exception e) {
            log.error("MongoTemplate 初始化失败: {}", e.getMessage());
            throw new MongoException("MongoTemplate 初始化失败: " + e.getMessage());
        }
    }

    // Getter 和 Setter
    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public int getChunkSizeBytes() {
        return chunkSizeBytes;
    }

    public void setChunkSizeBytes(int chunkSizeBytes) {
        this.chunkSizeBytes = chunkSizeBytes;
    }
}
