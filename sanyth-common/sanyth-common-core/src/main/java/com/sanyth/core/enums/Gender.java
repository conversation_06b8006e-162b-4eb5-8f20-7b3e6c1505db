package com.sanyth.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanyth.core.config.enumConverterFactory.ValueTextBaseEnum;
import org.springframework.util.StringUtils;

public enum Gender implements ValueTextBaseEnum {
    male(1, "男"),
    female(2, "女");
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;

    Gender(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static Gender get(String text) {
        if (!StringUtils.hasLength(text))
            return null;
        for (Gender gender : Gender.values()) {
            boolean equals = gender.getText().equals(text);
            if (equals) {
                return gender;
            }
        }
        return null;
    }
}
