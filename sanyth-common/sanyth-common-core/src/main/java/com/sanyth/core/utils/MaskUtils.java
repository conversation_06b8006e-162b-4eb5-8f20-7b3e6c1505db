package com.sanyth.core.utils;

import org.springframework.util.StringUtils;

public class MaskUtils {
    public static String idCardNum(String idCardNum) {
        if (StringUtils.hasLength(idCardNum))
            return idCardNum.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1****$2");
        return idCardNum;
    }

    public static String mobilePhone(String mobilePhone) {
        if (StringUtils.hasLength(mobilePhone))
            return mobilePhone.replaceAll("(?<=\\d{3})\\d{4}(?=\\d{4})", "****");
        return mobilePhone;
    }

    public static String email(String email) {
        int visibleChars = 3;
        if (StringUtils.hasLength(email)) {
            int atIndex = email.indexOf('@');
            if (atIndex <= visibleChars * 2) {
                return email.substring(0, atIndex).replaceAll(".", "*") + email.substring(atIndex);
            } else {
                return email.substring(0, visibleChars)
                        .replaceAll(".", "*")
                        + email.substring(atIndex - visibleChars, atIndex)
                        + email.substring(atIndex);
            }
        }
        return email;
    }

    public static String apiKey(String apiKey) {
        if (StringUtils.hasLength(apiKey)) {
            if (apiKey.length() <= 8) {
                return apiKey;
            }
            // 保留前4位和后4位，中间用星号替代
            return apiKey.substring(0, 4)
                    + "*".repeat(apiKey.length() - 8)
                    + apiKey.substring(apiKey.length() - 4);
        }
        return apiKey;
    }
}
