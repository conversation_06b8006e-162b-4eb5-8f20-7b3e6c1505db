package com.sanyth.core.mybatisplus;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.sanyth.core.constant.Constants;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by JIANGPING on 2024/10/14.
 */
public class MyBatisPlusUtil {

    public static List<OrderItem> parseOrderSQL(String orderSQL, boolean isToUnderlineCase) {
        List<OrderItem> orders = new ArrayList<>();
        if (StrUtil.isNotBlank(orderSQL)) {
            for (String item : orderSQL.split(",")) {
                String[] temp = item.trim().split(" ");
                if (!temp[0].isEmpty()) {
                    String column = isToUnderlineCase ? StrUtil.toUnderlineCase(temp[0]) : temp[0];
                    boolean asc = temp.length == 1 || !temp[temp.length - 1].equals(Constants.ORDER_DESC_VALUE);
                    orders.add(new OrderItem().setColumn(column).setAsc(asc));
                }
            }
        }
        return orders;
    }

    /**
     * 构建排序字段集合
     *
     * @param sort
     * @param order
     * @param isToUnderlineCase
     * @return
     */
    public static List<OrderItem> orderItems(String sort, String order, boolean isToUnderlineCase) {
        List<OrderItem> orderItems = new ArrayList<>();
        if (sortIsSQL(sort)) {
            orderItems = parseOrderSQL(sort, isToUnderlineCase);
        } else {
            String column = isToUnderlineCase ? StrUtil.toUnderlineCase(sort) : sort;
            boolean asc = !Constants.ORDER_DESC_VALUE.equals(order);
            orderItems.add(new OrderItem().setColumn(column).setAsc(asc));
        }
        return orderItems;
    }

    private static boolean sortIsSQL(String sort) {
        return sort != null && (sort.contains(",") || sort.trim().contains(" "));
    }
}
