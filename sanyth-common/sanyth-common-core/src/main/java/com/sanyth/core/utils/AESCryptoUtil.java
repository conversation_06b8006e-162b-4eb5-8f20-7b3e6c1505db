package com.sanyth.core.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加密工具类
 */
@Slf4j
public class AESCryptoUtil {
    public static final String AESInitVector = "AESInitVector";
    public static final String AESEncryptKey = "AESEncryptKey";

    /**
     * 加密
     *
     * @param str        加密内容
     * @param initVector 偏移量
     * @param encryptKey 加密key
     * @return
     */
    public static String encrypt(String str, String initVector, String encryptKey) throws Exception {
        IvParameterSpec ivParameterSpec = new IvParameterSpec(initVector.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(encryptKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher instance = Cipher.getInstance("AES/CBC/NoPadding");
        instance.init(1, secretKeySpec, ivParameterSpec);
        int blockSize = instance.getBlockSize();
        byte[] bytes = str.getBytes();
        int length = bytes.length;
        if (length % blockSize != 0) {
            length += blockSize - (length % blockSize);
        }
        byte[] bArr = new byte[length];
        System.arraycopy(bytes, 0, bArr, 0, bytes.length);
        return new String(Base64.getEncoder().encode(instance.doFinal(bArr)));
    }


    /**
     * 解密
     *
     * @param str        解密字符串
     * @param initVector 偏移量
     * @param encryptKey 解密key
     * @return
     */
    public static String decrypt(String str, String initVector, String encryptKey) throws Exception {
        IvParameterSpec ivParameterSpec = new IvParameterSpec(initVector.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(encryptKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher instance = Cipher.getInstance("AES/CBC/NoPadding");
        instance.init(2, secretKeySpec, ivParameterSpec);
        return new String(instance.doFinal(Base64.getDecoder().decode(str)), StandardCharsets.UTF_8).trim();
    }

    public static String encrypt(String str) throws Exception {
        return encrypt(str, "sanythsanyth1234", "sanyth123!123456");
    }

    public static String decrypt(String str) throws Exception {
        return decrypt(str, "sanythsanyth1234", "sanyth123!123456");
    }

    public static void main(String[] strArr) {
        try {
            String INIT_VECTOR = "sanythsanyth1234";
            String KEY = "sanyth123!123456";

            String str = "10001";
            String s = encrypt(str, INIT_VECTOR, KEY);
            System.out.println(s);
            System.out.println(decrypt("vD0z9M6mucPCCLOMODN+8A==", INIT_VECTOR, KEY));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
