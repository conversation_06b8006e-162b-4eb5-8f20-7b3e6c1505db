package com.sanyth.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanyth.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum JudgeMark implements ValueTextBaseEnum {

    yes(1, "是", true), no(0, "否", false);

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private Boolean mark;

    JudgeMark(Integer value, String text, Boolean mark) {
        this.value = value;
        this.text = text;
        this.mark = mark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public Boolean getMark() {
        return mark;
    }
}
