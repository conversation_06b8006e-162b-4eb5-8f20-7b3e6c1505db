package com.sanyth.core.utils.rsa;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import java.io.*;
import java.math.BigInteger;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.RSAPrivateKeySpec;
import java.security.spec.RSAPublicKeySpec;
import java.text.SimpleDateFormat;
import java.util.Base64;

@Slf4j
public class RSAUtils {

    private static final String ALGORITHOM = "RSA";
    private static final int KEY_SIZE = 1024;
    private static final Provider DEFAULT_PROVIDER = new BouncyCastleProvider();
    private static KeyPairGenerator keyPairGen = null;
    private static KeyFactory keyFactory = null;
    private static KeyPair oneKeyPair = null;
    private static File rsaPairFile = null;

    static {
        try {
            keyPairGen = KeyPairGenerator.getInstance("RSA", DEFAULT_PROVIDER);
            keyFactory = KeyFactory.getInstance("RSA", DEFAULT_PROVIDER);
            rsaPairFile = new File(getRSAPairFilePath());
        } catch (NoSuchAlgorithmException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private static synchronized KeyPair generateKeyPair() {
        try {
            String str = new SimpleDateFormat("yyyyMMdd").format(System.currentTimeMillis());
            keyPairGen.initialize(KEY_SIZE, new SecureRandom(str.getBytes()));
            oneKeyPair = keyPairGen.generateKeyPair();
            saveKeyPair(oneKeyPair);
            return oneKeyPair;
        } catch (InvalidParameterException ex) {
            log.error("KeyPairGenerator does not support a key length of 1024.", ex);
        } catch (NullPointerException ex) {
            log.error("RSAUtils#KEY_PAIR_GEN is null, can not generate KeyPairGenerator instance.",
                    ex);
        }
        return null;
    }

    private static String getRSAPairFilePath() {
//        String rsa_para_file = ResourceBundle.getBundle("rsa", Locale.getDefault(), RSAUtils.class.getClassLoader()).getString("rsa.pair.file");
        String rsa_para_file = new ClassPathResource("__RSA_PAIR").getPath();
        return rsa_para_file;
    }

    private static boolean isCreateKeyPairFile() {
        boolean createNewKeyPair = false;
        if ((!(rsaPairFile.exists())) || (rsaPairFile.isDirectory())) {
            createNewKeyPair = true;
        }
        return createNewKeyPair;
    }

    private static void saveKeyPair(KeyPair keyPair) {
        FileOutputStream fos = null;
        ObjectOutputStream oos = null;
        try {
            fos = new FileOutputStream(rsaPairFile);
            oos = new ObjectOutputStream(fos);
            oos.writeObject(keyPair);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (oos != null) oos.close();
                if (fos != null) fos.close();
            } catch (Exception localException2) {
            }
        }
    }

    public static KeyPair getKeyPair() {
        if (isCreateKeyPairFile()) {
            return generateKeyPair();
        }
        if (oneKeyPair != null) {
            return oneKeyPair;
        }
        return readKeyPair();
    }

    private static KeyPair readKeyPair() {
        FileInputStream fis = null;
        ObjectInputStream ois = null;
        try {
            String rsaPair = new ClassPathResource("__RSA_PAIR").getPath();
            fis = new FileInputStream(rsaPair);
//            fis = new FileInputStream(rsaPairFile);
            ois = new ObjectInputStream(fis);
            oneKeyPair = (KeyPair) ois.readObject();
            return oneKeyPair;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (ois != null) ois.close();
                if (fis != null) fis.close();
            } catch (Exception localException3) {
            }
        }
        return null;
    }

    public static RSAPublicKey generateRSAPublicKey(byte[] modulus, byte[] publicExponent) {
        RSAPublicKeySpec publicKeySpec = new RSAPublicKeySpec(new BigInteger(modulus),
                new BigInteger(publicExponent));
        try {
            return ((RSAPublicKey) keyFactory.generatePublic(publicKeySpec));
        } catch (InvalidKeySpecException ex) {
            log.error("RSAPublicKeySpec is unavailable.", ex);
        } catch (NullPointerException ex) {
            log.error("RSAUtils#KEY_FACTORY is null, can not generate KeyFactory instance.", ex);
        }
        return null;
    }

    public static RSAPrivateKey generateRSAPrivateKey(byte[] modulus, byte[] privateExponent) {
        RSAPrivateKeySpec privateKeySpec = new RSAPrivateKeySpec(new BigInteger(modulus),
                new BigInteger(privateExponent));
        try {
            return ((RSAPrivateKey) keyFactory.generatePrivate(privateKeySpec));
        } catch (InvalidKeySpecException ex) {
            log.error("RSAPrivateKeySpec is unavailable.", ex);
        } catch (NullPointerException ex) {
            log.error("RSAUtils#KEY_FACTORY is null, can not generate KeyFactory instance.", ex);
        }
        return null;
    }

    public static RSAPrivateKey getRSAPrivateKey(String hexModulus, String hexPrivateExponent) {
        if (!StringUtils.hasLength(hexModulus) || !StringUtils.hasLength(hexPrivateExponent)) {
            if (log.isDebugEnabled()) {
                log.debug("hexModulus and hexPrivateExponent cannot be empty. RSAPrivateKey value is null to return.");
            }
            return null;
        }
        byte[] modulus = Hex.decode(hexModulus);
        byte[] privateExponent = Hex.decode(hexPrivateExponent);
        if ((modulus != null) && (privateExponent != null)) {
            return generateRSAPrivateKey(modulus, privateExponent);
        }
        return null;
    }

    public static RSAPublicKey getRSAPublicKey(String hexModulus, String hexPublicExponent) {
        if (!StringUtils.hasLength(hexModulus) || !StringUtils.hasLength(hexPublicExponent)) {
            if (log.isDebugEnabled()) {
                log.debug("hexModulus and hexPublicExponent cannot be empty. return null(RSAPublicKey).");
            }
            return null;
        }
        byte[] modulus = Hex.decode(hexModulus);
        byte[] publicExponent = Hex.decode(hexPublicExponent);
        if ((modulus != null) && (publicExponent != null)) {
            return generateRSAPublicKey(modulus, publicExponent);
        }
        return null;
    }

    public static byte[] encrypt(PublicKey publicKey, byte[] data)
            throws Exception {
        Cipher ci = Cipher.getInstance("RSA", DEFAULT_PROVIDER);
        ci.init(1, publicKey);
        return ci.doFinal(data);
    }

    public static byte[] decrypt(PrivateKey privateKey, byte[] data)
            throws Exception {
        Cipher ci = Cipher.getInstance("RSA", DEFAULT_PROVIDER);
        ci.init(2, privateKey);
        return ci.doFinal(data);
    }

    public static String encryptString(PublicKey publicKey, String plaintext) {
        if ((publicKey == null) || (plaintext == null)) {
            return null;
        }
        byte[] data = plaintext.getBytes();
        try {
            byte[] en_data = encrypt(publicKey, data);
            return new String(Hex.encode(en_data));
        } catch (Exception ex) {
            log.error(ex.getCause().getMessage());
        }
        return null;
    }

    public static String encryptString(String plaintext) {
        if (plaintext == null) {
            return null;
        }
        byte[] data = plaintext.getBytes();
        KeyPair keyPair = getKeyPair();
        try {
            byte[] en_data = encrypt((RSAPublicKey) keyPair.getPublic(), data);
            return new String(Hex.encode(en_data));
        } catch (NullPointerException ex) {
            log.error("keyPair cannot be null.");
        } catch (Exception ex) {
            log.error(ex.getCause().getMessage());
        }
        return null;
    }

    public static String decryptString(PrivateKey privateKey, String encrypttext) {
        if ((privateKey == null) || !StringUtils.hasLength(encrypttext))
            return null;
        try {
            byte[] en_data = Hex.decode(encrypttext);
            byte[] data = decrypt(privateKey, en_data);
            return new String(data);
        } catch (Exception ex) {
            log.error(String.format("\"%s\" Decryption failed. Cause: %s", new Object[]{encrypttext, ex.getCause().getMessage()}));
        }
        return null;
    }

    public static String decryptString(String encrypttext) {
        if (!StringUtils.hasLength(encrypttext)) {
            return null;
        }
        KeyPair keyPair = getKeyPair();
        try {
            byte[] en_data = Hex.decode(encrypttext);
            byte[] data = decrypt((RSAPrivateKey) keyPair.getPrivate(), en_data);
            return new String(data);
        } catch (NullPointerException ex) {
            log.error("keyPair cannot be null.");
        } catch (Exception ex) {
            log.error(String.format("\"%s\" Decryption failed. Cause: %s", new Object[]{encrypttext, ex.getMessage()}));
        }
        return null;
    }

    public static String decryptStringByJs(String encrypttext) {
        String text = decryptString(encrypttext);
        if (text == null) {
            return null;
        }
        return new StringBuilder(text).reverse().toString();
    }

    public static RSAPublicKey getDefaultPublicKey() {
        KeyPair keyPair = getKeyPair();
        if (keyPair != null) {
            return ((RSAPublicKey) keyPair.getPublic());
        }
        return null;
    }

    public static RSAPrivateKey getDefaultPrivateKey() {
        KeyPair keyPair = getKeyPair();
        if (keyPair != null) {
            return ((RSAPrivateKey) keyPair.getPrivate());
        }
        return null;
    }

    public static PublicKeyMap getPublicKeyMap() {
        PublicKeyMap publicKeyMap = new PublicKeyMap();
        RSAPublicKey rsaPublicKey = getDefaultPublicKey();
        publicKeyMap.setModulus(new String(Hex.encode(rsaPublicKey.getModulus().toByteArray())));
        publicKeyMap.setExponent(new String(Hex.encode(rsaPublicKey.getPublicExponent().toByteArray())));
        return publicKeyMap;
    }

    public static byte[] decryptBASE64(String key) {
        return Base64.getDecoder().decode(key);
    }

    public static String encryptBASE64(byte[] key) {
        return Base64.getEncoder().encodeToString(key);
    }

    public static void main(String[] args)
            throws Exception {
        String js = "31a40b09ea61d6772fe0bf2a6c4c04431da736ec65c522449660a7ff2784e177087b5fe8a5887ed8a58b8597e47613410e76e500c92b095c918516193d9b300bf442a3b469f072dae01598a5c0b6b1ecde98154efc798d5819b7731bbc715a7a3427daa6a5d01ecf50b8837db7c4b2ceea9c0082869b2737f8106fcd9cdb770a";
        js = "3f994d846f814895abad03b505d156a8e8846a5967397f32c0062b5bd41c08c7b1ccbebd52aa4b0c4028f3798d1433519cb170a83d141ad06475a993689eb210b34029fa6bc24bd238b746556c557d7e27e78ca3accda8e2c73698ccf2877c2ea26c8f2ca4e8e743d1b67db43583d9acc726cfba685f67bc15af9e1689850ae5";
        String descyptstr = decryptStringByJs(js);
        System.out.println(descyptstr);
//        System.out.println("decryptStringByJs=====" + descyptstr);
//        System.out.println("decryptStringByJs.decode=====" + URLDecoder.decode(descyptstr, "UTF-8"));
        System.out.println(encryptString("11111111111111"));
    }

}
