package com.sanyth.core.web;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
public class ExcelImportError implements Serializable {
    /**
     * 行号
     */
    @ExcelProperty(value = "行号", index = 0)
    private Integer line;
    /**
     * 导入原始值
     */
    @ColumnWidth(value = 10)
    @ExcelProperty(value = "原始值", index = 1)
    private String value;
    /**
     * 错误描述
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "错误描述", index = 2)
    private String remark;

    public static ExcelImportError data(Integer line, Object value, String remark) {
        return new ExcelImportError(line, value != null ? String.valueOf(value) : "", remark);
    }
}
