package com.sanyth.core.web;

/**
 * <AUTHOR>
 * @since 2021-10-22
 */
public class ColumnType {
    public static final String CHAR_32 = "VARCHAR2(50)";    // 注：不足32位ORACLE会自动补全空格
    public static final String VARCHAR2_32 = "VARCHAR2(32)";
    public static final String VARCHAR2_512 = "VARCHAR2(512)";
    public static final String VARCHAR2_1024 = "VARCHAR2(1024)";

    public static final String NUMBER_1 = "NUMBER(1)";
    public static final String NUMBER_10 = "NUMBER(10)";
    public static final String NUMBER_1_DEFAULT_1 = "NUMBER(1) DEFAULT 1";
    public static final String NUMBER_1_DEFAULT_0 = "NUMBER(1) DEFAULT 0";
    public static final String CLOB = "CLOB";
}
