package com.sanyth.core.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.constant.Constants;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

@Getter
public class SortHelper<T> {

    private T entity;

    private Integer total;

    private Integer last;

    /**
     * 构造方法
     *
     * @param service    service
     * @param sortColumn 排序字段列名
     */
    public SortHelper(@NotNull IService<T> service, @NotEmpty String sortColumn) {
        try {
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            sortQuery(service, sortColumn, queryWrapper);
        } catch (IllegalAccessException e) {
            AssertUtil.throwMessage(Constants.RESULT_ERROR_MSG);
        }
    }

    /**
     * 构造方法
     *
     * @param service
     * @param sortColumn
     * @param queryWrapper
     */
    public SortHelper(@NotNull IService<T> service, @NotEmpty String sortColumn, @NotNull QueryWrapper<T> queryWrapper) {
        try {
            sortQuery(service, sortColumn, queryWrapper);
        } catch (IllegalAccessException e) {
            AssertUtil.throwMessage(Constants.RESULT_ERROR_MSG);
        }
    }

    private void sortQuery(IService<T> service, String sortColumn, QueryWrapper<T> queryWrapper) throws IllegalAccessException {
        Page<T> page = new Page<>(1, 1);
        queryWrapper.select(sortColumn);
        queryWrapper.orderByDesc(sortColumn);
        page = service.page(page, queryWrapper);
        total = Integer.valueOf(page.getTotal() + "");
        String sortField = StrUtil.toCamelCase(sortColumn);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            entity = page.getRecords().get(0);
            if (entity == null) return;
            Field field = ReflectionUtils.findField(entity.getClass(), sortField);
            field.setAccessible(true);
            Object val = field.get(entity);
            last = (val == null ? null : Integer.valueOf(val.toString()));
        }
    }

    public int next() {
        return next(total, last);
    }

    public int next(Integer total, Integer last) {
        int sort = 1;
        int pageSize = 10;
        if (total == null || total.intValue() == 0) return sort;
        if (last != null) {
            sort = pageSize + last;
        } else {
            int page = (int) Math.ceil(total / (double) pageSize);
            sort = ((page + 1) * pageSize) + 1;
        }
        return sort;
    }
}
