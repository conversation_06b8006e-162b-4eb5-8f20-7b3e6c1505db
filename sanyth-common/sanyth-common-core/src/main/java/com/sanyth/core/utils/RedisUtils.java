package com.sanyth.core.utils;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.sanyth.core.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 对Redis的某些操作二次封装
 */
@Component
@Slf4j
public class RedisUtils {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public String get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("RedisUtils#get fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public String getAndExpire(String key, long seconds) {
        try {
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
            }
            return value;
        } catch (Exception e) {
            log.error("RedisUtils#getAndExpire fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public <T> T getObject(String key, Class<T> clazz) {
        try {
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                return new ObjectMapper().readValue(value, clazz);
            }
        } catch (Exception e) {
            log.error("RedisUtils#getObject fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public Boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("RedisUtils#hasKey fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    public Boolean delete(String key) {
        try {
            return redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("RedisUtils#delete fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    public Boolean set(String key, String value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils#set fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    public Boolean setIfAbsent(String key, String value, long seconds) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value, seconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("RedisUtils#setIfAbsent fail! e:{}", Throwables.getStackTraceAsString(e));
        }
        return false;
    }


}
