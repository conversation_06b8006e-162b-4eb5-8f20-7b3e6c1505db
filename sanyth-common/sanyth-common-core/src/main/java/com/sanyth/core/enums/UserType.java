package com.sanyth.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanyth.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * 用户类别
 */
public enum UserType implements ValueTextBaseEnum {
    student(1, "student", "学生"),
    teacher(2, "teacher", "教师"),
    other(3, "other", "其他");
    
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private String remark;

    UserType(Integer value, String text, String remark) {
        this.value = value;
        this.text = text;
        this.remark = remark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
