[2m2025-08-04 09:56:35.498[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 09:56:35.837[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 21668 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-08-04 09:56:35.838[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 09:56:35.838[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 09:56:35.927[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-08-04 09:56:35.927[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 09:56:35.927[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 09:56:35.927[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 09:56:37.915[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 09:56:37.918[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 09:56:38.572[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 638 ms. Found 10 JPA repository interfaces.
[2m2025-08-04 09:56:38.582[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 09:56:38.583[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 09:56:38.618[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.618[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.619[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.620[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.620[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 09:56:38.620[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 34 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 09:56:38.631[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 09:56:38.633[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.674[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.675[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.675[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.675[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 09:56:38.675[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 09:56:40.466[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 9096 (http)
[2m2025-08-04 09:56:40.512[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-9096"]
[2m2025-08-04 09:56:40.516[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 09:56:40.520[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 09:56:40.639[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 09:56:40.644[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4716 ms
[2m2025-08-04 09:56:41.544[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@3e6be3c1], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5c5812c, com.mongodb.Jep395RecordCodecProvider@77de3401, com.mongodb.KotlinCodecProvider@30350c73]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@64e06cf0], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 09:56:42.249[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 09:56:42.535[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=140550542, minRoundTripTimeNanos=0}
[2m2025-08-04 09:56:42.705[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 09:56:42.719[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanyth.core.handler.DecryptInterceptor@39f29540'
[2m2025-08-04 09:56:42.719[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@fd6784a'
[2m2025-08-04 09:56:42.719[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@1a5247c8'
[2m2025-08-04 09:56:43.256[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-04 09:56:43.337[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 09:56:43.392[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-04 09:56:43.440[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 09:56:43.490[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-04 09:56:43.530[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 09:56:43.563[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 09:56:43.598[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 09:56:43.633[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 09:56:43.709[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 09:56:43.750[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 09:56:43.782[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 09:56:43.809[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 09:56:43.867[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 09:56:43.926[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:9
[2m2025-08-04 09:56:44.046[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.core.config.MongoConfig      [0;39m [2m:[0;39m GridFSBucket 初始化成功 - 数据库: server, Bucket: fs, 块大小: 255KB
[2m2025-08-04 09:56:44.235[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 09:56:44.324[0;39m [31mERROR[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 09:56:44.742[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-04 09:56:47.740[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-04 09:56:47.995[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 09:56:48.280[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 09:56:48.349[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 09:56:48.439[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 09:56:48.744[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 09:56:48.870[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 09:56:50.502[0;39m [33m WARN[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 09:56:50.728[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 09:56:42",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1637349983, ConnectTime:"2025-08-04 09:56:50", UseCount:1, LastActiveTime:"2025-08-04 09:56:50"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 09:56:52.233[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 09:56:52.249[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 09:56:53.447[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 09:56:53.495[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 09:56:53.500[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 09:56:53.516[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 09:56:53.554[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:53.642[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 09:56:53.950[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- CHAT， 模型配置：AigcModel(id=b69a8765b69ccd2c59e646a926f55fb2, type=CHAT, model=qwen2.5, provider=OLLAMA, name=Ollama/qwen2.5, responseLimit=2033, temperature=0.2, topP=0.3, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 09:56:53.954[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- CHAT， 模型配置：AigcModel(id=eddd81d38381ff475f735e539b163e74, type=CHAT, model=Qwen/Qwen3-8B, provider=OPENAI, name=Qwen3-8B, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 09:56:53.954[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- EMBEDDING， 模型配置：AigcModel(id=097a00062ad6110d1c86e6fcc1391791, type=EMBEDDING, model=BAAI/bge-m3, provider=OPENAI, name=bge-m3, responseLimit=2000, temperature=0.2, topP=0.5, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 09:56:53.954[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=618b4c8f87bca57686748264817b34dc, type=EMBEDDING, model=bge-m3, provider=OLLAMA, name=Ollama/bge-m3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 09:56:54.014[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:54.022[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:54.024[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:54.024[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:54.024[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:54.096[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 09:56:54.923[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.c.provider.EmbeddingStoreFactory  [0;39m [2m:[0;39m 已成功注册Embedding Store：PGVECTOR， 配置信息：AigcEmbedStore(id=8eead31ed55dadfd634f6118cad0e5c7, name=Pgvector, provider=PGVECTOR, host=*************, username=root, dimension=1024, databaseName=langchain, port=5432, tableName=langchain_store, password=langchain123456)
[2m2025-08-04 09:56:55.052[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 09:56:55.058[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 09:56:55.059[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 09:56:55.060[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 09:56:55.060[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:55.125[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 7
[2m2025-08-04 09:56:55.126[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 09:56:55.133[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 09:56:55.135[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 09:56:55.135[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 09:56:55.135[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:55.204[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 09:56:55.206[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:55.211[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:55.213[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:55.213[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 09:56:55.213[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:55.281[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 09:56:55.540[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 初始化应用程序频道配置列表...
[2m2025-08-04 09:56:55.541[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 09:56:55.545[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 09:56:55.548[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 09:56:55.548[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 09:56:55.549[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:55.619[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 09:56:55.620[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 成功初始化 1 API通道
[2m2025-08-04 09:56:55.691[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Initializing app config list...
[2m2025-08-04 09:56:55.692[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 09:56:55.699[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 09:56:55.701[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 09:56:55.701[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 09:56:55.701[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 09:56:55.843[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 09:56:55.845[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Successfully initialized 3 apps
[2m2025-08-04 09:56:56.764[0;39m [33m WARN[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 09:56:57.272[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 09:56:57.321[0;39m [33m WARN[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 2bb6b72f-adc4-4783-852e-b07dc555b4be

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 09:56:57.335[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 09:56:57.809[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-04 09:56:58.169[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 2bb6b72f-adc4-4783-852e-b07dc555b4be

[2m2025-08-04 09:56:58.337[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-9096"]
[2m2025-08-04 09:56:58.380[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 9096 (http) with context path '/'
[2m2025-08-04 09:56:58.395[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Started App in 23.713 seconds (process running for 25.939)
[2m2025-08-04 09:56:58.407[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 09:56:58.408[0;39m [32mDEBUG[0;39m [35m21668[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 09:56:58.870[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[on(4)-127.0.0.1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 09:56:58.870[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[on(4)-127.0.0.1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 09:56:58.874[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[on(4)-127.0.0.1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 4 ms
[2m2025-08-04 09:57:39.914[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-04 09:57:39.928[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-04 09:57:39.968[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 09:57:39.995[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-04 09:57:40.055[0;39m [32m INFO[0;39m [35m21668[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closed
[2m2025-08-04 10:54:11.796[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 10:54:12.199[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 31824 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-08-04 10:54:12.199[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 10:54:12.200[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 10:54:12.290[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-08-04 10:54:12.291[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 10:54:12.291[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 10:54:12.291[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 10:54:14.193[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 10:54:14.197[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 10:54:14.558[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 343 ms. Found 10 JPA repository interfaces.
[2m2025-08-04 10:54:14.567[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 10:54:14.568[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 10:54:14.600[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.601[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.602[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.602[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 10:54:14.602[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 33 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 10:54:14.612[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 10:54:14.614[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 10:54:14.653[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.653[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.653[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.653[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.653[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 10:54:14.654[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 10:54:15.924[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 9096 (http)
[2m2025-08-04 10:54:15.955[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-9096"]
[2m2025-08-04 10:54:15.957[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 10:54:15.957[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 10:54:16.021[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 10:54:16.021[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3730 ms
[2m2025-08-04 10:54:16.397[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@3f6fa2dd], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5f38dc75, com.mongodb.Jep395RecordCodecProvider@48e0d3a7, com.mongodb.KotlinCodecProvider@57a22410]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@2461a62c], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 10:54:16.537[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=81572666, minRoundTripTimeNanos=0}
[2m2025-08-04 10:54:16.629[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 10:54:16.835[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 10:54:16.840[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanyth.core.handler.DecryptInterceptor@69b9ea1d'
[2m2025-08-04 10:54:16.840[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4a270796'
[2m2025-08-04 10:54:16.840[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@3f58e7d5'
[2m2025-08-04 10:54:17.081[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-04 10:54:17.132[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 10:54:17.170[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-04 10:54:17.201[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 10:54:17.233[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-04 10:54:17.274[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 10:54:17.310[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 10:54:17.347[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 10:54:17.373[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 10:54:17.425[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 10:54:17.456[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 10:54:17.480[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 10:54:17.504[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 10:54:17.551[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 10:54:17.560[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:12
[2m2025-08-04 10:54:17.645[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.core.config.MongoConfig      [0;39m [2m:[0;39m GridFSBucket 初始化成功 - 数据库: server, Bucket: fs, 块大小: 255KB
[2m2025-08-04 10:54:17.809[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 10:54:17.886[0;39m [31mERROR[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 10:54:18.269[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-04 10:54:21.722[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-04 10:54:21.894[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 10:54:22.153[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 10:54:22.226[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 10:54:22.302[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 10:54:22.620[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 10:54:22.750[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 10:54:24.651[0;39m [33m WARN[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 10:54:25.030[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 10:54:16",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:2073745358, ConnectTime:"2025-08-04 10:54:24", UseCount:1, LastActiveTime:"2025-08-04 10:54:25"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 10:54:26.463[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 10:54:26.473[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 10:54:27.647[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 10:54:27.696[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 10:54:27.701[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 10:54:27.721[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 10:54:27.762[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:27.851[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 10:54:28.222[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- CHAT， 模型配置：AigcModel(id=b69a8765b69ccd2c59e646a926f55fb2, type=CHAT, model=qwen2.5, provider=OLLAMA, name=Ollama/qwen2.5, responseLimit=2033, temperature=0.2, topP=0.3, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 10:54:28.226[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- CHAT， 模型配置：AigcModel(id=eddd81d38381ff475f735e539b163e74, type=CHAT, model=Qwen/Qwen3-8B, provider=OPENAI, name=Qwen3-8B, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 10:54:28.226[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- EMBEDDING， 模型配置：AigcModel(id=097a00062ad6110d1c86e6fcc1391791, type=EMBEDDING, model=BAAI/bge-m3, provider=OPENAI, name=bge-m3, responseLimit=2000, temperature=0.2, topP=0.5, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 10:54:28.226[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=618b4c8f87bca57686748264817b34dc, type=EMBEDDING, model=bge-m3, provider=OLLAMA, name=Ollama/bge-m3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 10:54:28.290[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:28.296[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:28.297[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:28.298[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:28.298[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:28.725[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 10:54:29.594[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.c.provider.EmbeddingStoreFactory  [0;39m [2m:[0;39m 已成功注册Embedding Store：PGVECTOR， 配置信息：AigcEmbedStore(id=8eead31ed55dadfd634f6118cad0e5c7, name=Pgvector, provider=PGVECTOR, host=*************, username=root, dimension=1024, databaseName=langchain, port=5432, tableName=langchain_store, password=langchain123456)
[2m2025-08-04 10:54:29.738[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 10:54:29.743[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 10:54:29.745[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 10:54:29.745[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 10:54:29.746[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:29.811[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 7
[2m2025-08-04 10:54:29.811[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 10:54:29.818[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 10:54:29.819[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 10:54:29.820[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 10:54:29.820[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:29.885[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 10:54:29.887[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:29.892[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:29.894[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:29.894[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 10:54:29.894[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:29.965[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 10:54:30.230[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 初始化应用程序频道配置列表...
[2m2025-08-04 10:54:30.231[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 10:54:30.235[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 10:54:30.237[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 10:54:30.237[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 10:54:30.237[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:30.302[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 10:54:30.303[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 成功初始化 1 API通道
[2m2025-08-04 10:54:30.366[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Initializing app config list...
[2m2025-08-04 10:54:30.367[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 10:54:30.372[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 10:54:30.374[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 10:54:30.375[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 10:54:30.375[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 10:54:30.501[0;39m [32mDEBUG[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 10:54:30.502[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Successfully initialized 3 apps
[2m2025-08-04 10:54:31.384[0;39m [33m WARN[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 10:54:31.860[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 10:54:31.906[0;39m [33m WARN[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 2e841654-8fd4-4ff7-849b-e9a4c009c31e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 10:54:31.915[0;39m [32m INFO[0;39m [35m31824[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 11:01:15.974[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 11:01:16.280[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 34011 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-08-04 11:01:16.281[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 11:01:16.281[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 11:01:16.365[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-08-04 11:01:16.365[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 11:01:16.365[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 11:01:16.365[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 11:01:18.165[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 11:01:18.168[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 11:01:18.535[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 350 ms. Found 10 JPA repository interfaces.
[2m2025-08-04 11:01:18.544[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 11:01:18.544[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 11:01:18.577[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.579[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.579[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.579[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 11:01:18.579[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 33 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 11:01:18.589[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 11:01:18.590[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.628[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.629[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 11:01:18.629[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 11:01:19.867[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 9096 (http)
[2m2025-08-04 11:01:19.902[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-9096"]
[2m2025-08-04 11:01:19.905[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 11:01:19.905[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 11:01:19.973[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 11:01:19.973[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3607 ms
[2m2025-08-04 11:01:20.344[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@6de4e34a], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@775b3762, com.mongodb.Jep395RecordCodecProvider@114b81af, com.mongodb.KotlinCodecProvider@4ef435f3]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[127.0.0.1:28018], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@3e1201dd], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 11:01:20.578[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 11:01:20.755[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 11:01:20.759[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[127.0.0.1:28018][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=127.0.0.1:28018, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=9, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=421973666, minRoundTripTimeNanos=0}
[2m2025-08-04 11:01:20.764[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanyth.core.handler.DecryptInterceptor@718fb7a2'
[2m2025-08-04 11:01:20.764[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@6cd79426'
[2m2025-08-04 11:01:20.764[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@34a82874'
[2m2025-08-04 11:01:20.973[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-04 11:01:21.027[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 11:01:21.062[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-04 11:01:21.091[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 11:01:21.119[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-04 11:01:21.157[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 11:01:21.188[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 11:01:21.224[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 11:01:21.249[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 11:01:21.298[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 11:01:21.324[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 11:01:21.361[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 11:01:21.382[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 11:01:21.433[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 11:01:21.444[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:31
[2m2025-08-04 11:01:21.525[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.core.config.MongoConfig      [0;39m [2m:[0;39m GridFSBucket 初始化成功 - 数据库: server, Bucket: fs, 块大小: 255KB
[2m2025-08-04 11:01:21.682[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 11:01:21.756[0;39m [31mERROR[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 11:01:22.141[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-04 11:01:25.258[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-04 11:01:25.408[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 11:01:25.630[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 11:01:25.680[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 11:01:25.737[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 11:01:25.979[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 11:01:26.083[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 11:01:29.203[0;39m [33m WARN[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 11:01:29.885[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 11:01:20",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1961899429, ConnectTime:"2025-08-04 11:01:28", UseCount:1, LastActiveTime:"2025-08-04 11:01:29"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 11:01:31.469[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 11:01:31.482[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 11:01:32.594[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 11:01:32.635[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 11:01:32.640[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 11:01:32.654[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 11:01:32.689[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:32.918[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 11:01:33.193[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- CHAT， 模型配置：AigcModel(id=eddd81d38381ff475f735e539b163e74, type=CHAT, model=Qwen/Qwen3-8B, provider=OPENAI, name=Qwen3-8B, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 11:01:33.198[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- EMBEDDING， 模型配置：AigcModel(id=097a00062ad6110d1c86e6fcc1391791, type=EMBEDDING, model=BAAI/bge-m3, provider=OPENAI, name=bge-m3, responseLimit=2000, temperature=0.2, topP=0.5, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-04 11:01:33.252[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:33.259[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:33.259[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:33.259[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:33.259[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:33.472[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:01:33.527[0;39m [31mERROR[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.c.provider.EmbeddingStoreFactory  [0;39m [2m:[0;39m 向量数据库初始化失败：[Pgvector] --- [PGVECTOR]，数据库配置信息：[AigcEmbedStore(id=8eead31ed55dadfd634f6118cad0e5c7, name=Pgvector, provider=PGVECTOR, host=127.0.0.1, username=root, dimension=1024, databaseName=langchain, port=5432, tableName=langchain_store, password=langchain123456)]
[2m2025-08-04 11:01:33.628[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 11:01:33.633[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-04 11:01:33.634[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 11:01:33.634[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-04 11:01:33.634[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:33.860[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 7
[2m2025-08-04 11:01:33.861[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 11:01:33.866[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-04 11:01:33.868[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 11:01:33.868[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-04 11:01:33.868[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:34.077[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 11:01:34.078[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:34.084[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:34.085[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:34.086[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-04 11:01:34.086[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:34.298[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:01:34.548[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 初始化应用程序频道配置列表...
[2m2025-08-04 11:01:34.549[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 11:01:34.553[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-04 11:01:34.554[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 11:01:34.554[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-04 11:01:34.554[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:34.757[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:01:34.761[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 成功初始化 1 API通道
[2m2025-08-04 11:01:34.841[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Initializing app config list...
[2m2025-08-04 11:01:34.842[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 11:01:34.847[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-04 11:01:34.848[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 11:01:34.849[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-04 11:01:34.849[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:01:35.108[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:01:35.109[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Successfully initialized 1 apps
[2m2025-08-04 11:01:35.868[0;39m [33m WARN[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-04 11:01:36.299[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-04 11:01:36.345[0;39m [33m WARN[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 02316c56-a434-4695-ac78-9de98d176a6d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 11:01:36.356[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 11:01:36.760[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-04 11:01:37.074[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 02316c56-a434-4695-ac78-9de98d176a6d

[2m2025-08-04 11:01:37.228[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-9096"]
[2m2025-08-04 11:01:37.265[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 9096 (http) with context path '/'
[2m2025-08-04 11:01:37.279[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Started App in 22.043 seconds (process running for 23.854)
[2m2025-08-04 11:01:37.290[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 11:01:37.291[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 11:02:18.122[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 11:02:18.126[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 11:02:18.132[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 6 ms
[2m2025-08-04 11:02:21.399[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode LIKE ?) ORDER BY create_time DESC
[2m2025-08-04 11:02:21.411[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode LIKE ?) ORDER BY create_time DESC
[2m2025-08-04 11:02:21.413[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode LIKE ?) ORDER BY create_time DESC
[2m2025-08-04 11:02:21.421[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode LIKE ?) ORDER BY create_time DESC
[2m2025-08-04 11:02:21.438[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: %system-info%(String)
[2m2025-08-04 11:02:21.698[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 11:02:48.084[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:02:48.098[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:02:48.101[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:02:48.102[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:02:48.103[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String), admin(String)
[2m2025-08-04 11:02:48.312[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:02:48.316[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 11:02:48.330[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 11:02:48.334[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 11:02:48.335[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 11:02:48.335[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-04 11:02:48.561[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 11:02:48.564[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-04 11:02:48.575[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-04 11:02:48.577[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-04 11:02:48.577[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-04 11:02:48.578[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-04 11:02:49.501[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 79
[2m2025-08-04 11:02:49.833[0;39m [31mERROR[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36mc.s.core.handler.GlobalExceptionHandler [0;39m [2m:[0;39m 用户名或密码错误

com.sanyth.core.exception.BusinessException: 用户名或密码错误
	at com.sanyth.core.utils.AssertUtil.throwMessage(AssertUtil.java:19)
	at com.sanyth.upms.system.controller.MainController.login(MainController.java:112)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:117)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-04 11:02:49.846[0;39m [33m WARN[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-9][0;39m [36m.m.m.a.ExceptionHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [BusinessException(code=1, data=null)]
[2m2025-08-04 11:02:59.991[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:03:00.008[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:03:00.010[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:03:00.011[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-04 11:03:00.011[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String), admin(String)
[2m2025-08-04 11:03:00.232[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:03:00.233[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 11:03:00.239[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 11:03:00.242[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 11:03:00.242[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 11:03:00.243[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-04 11:03:00.454[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 11:03:00.455[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-04 11:03:00.466[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-04 11:03:00.469[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-04 11:03:00.469[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-04 11:03:00.470[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-04 11:03:01.371[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 79
[2m2025-08-04 11:03:02.373[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-04 11:03:02.389[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-04 11:03:02.391[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-04 11:03:02.392[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==>  Preparing: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-04 11:03:02.394[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==> Parameters: admin(String)
[2m2025-08-04 11:03:02.621[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:04:00.244[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE
[2m2025-08-04 11:04:00.244[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    NAME,TYPE    FROM  SYT_SYS_ROLE_SCOPE
[2m2025-08-04 11:04:00.251[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    NAME,TYPE    FROM  SYT_SYS_ROLE_SCOPE
[2m2025-08-04 11:04:00.252[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT NAME, TYPE FROM SYT_SYS_ROLE_SCOPE
[2m2025-08-04 11:04:00.255[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE
[2m2025-08-04 11:04:00.255[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE
[2m2025-08-04 11:04:00.273[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT NAME, TYPE FROM SYT_SYS_ROLE_SCOPE
[2m2025-08-04 11:04:00.276[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:04:00.510[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mc.s.u.s.m.SysRoleScopeMapper.selectList [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 11:04:00.513[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_ROLE
[2m2025-08-04 11:04:00.514[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 11:04:00.682[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 11:04:00.690[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 11:04:00.694[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 11:04:00.856[0;39m [32mDEBUG[0;39m [35m34011[0;39m [2m---[0;39m [2m[nio-9096-exec-4][0;39m [36mc.s.u.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-08-04 11:07:38.576[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-04 11:07:38.590[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-04 11:07:38.681[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 11:07:38.707[0;39m [32m INFO[0;39m [35m34011[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
